import { Inject, Injectable, HttpException, HttpStatus } from '@nestjs/common';
import { Knex } from 'knex';
import { Article, ArticlesService } from 'src/articles/articles.service';
import { QueueService } from 'src/queue/queue.service';
import { <PERSON>ron } from '@nestjs/schedule';
import { sitemapsFromUrl } from './utils/sitemaps-from-url';
import { Website } from './models/website.interface';
import { articlesFromSitemap } from './utils/articles-from-sitemap';
import { formatMilliseconds } from './utils/format-milliseconds';
import { SitemapsService } from 'src/sitemaps/sitemaps.service';
import { SitemapArticle } from 'src/sitemap-articles/models/sitemap-article.interface';
import { AwsService } from 'src/aws/aws.service';
import {
  AdCheckJobErrorResult,
  AdCheckJobResult,
} from 'src/ad-checker/models/ad-check-job-result.interface';
import Bottleneck from 'bottleneck';

@Injectable()
export class WebsitesService {
  constructor(
    @Inject('KNEX_CONNECTION') private knex: Knex,
    private articlesService: ArticlesService,
    private queueService: QueueService,
    private sitemapsService: SitemapsService,
    private awsService: AwsService,
  ) {}

  async createWebsite(
    url: string,
    creator_id: string,
    ad_check_frequency: string,
    scrapping_enabled: boolean,
  ) {
    const payload = {
      url,
      creator_id,
      ad_check_frequency,
      scrapping_enabled,
    };

    const sitemapUrls = await sitemapsFromUrl(url);

    // If still no sitemaps found, throw an error
    if (sitemapUrls.length === 0) {
      throw new HttpException(
        'No sitemaps found for website',
        HttpStatus.BAD_REQUEST,
      );
    }

    const queryResponse = await this.knex<Website>('websites')
      .insert(payload)
      .returning('*');
    const website = queryResponse[0];

    const sitemaps = await Promise.all(
      sitemapUrls.map((url) =>
        this.sitemapsService.createSitemap(url, website.id),
      ),
    );

    for (const sitemap of sitemaps) {
      const articleUrls = await articlesFromSitemap(sitemap.url);

      for (const articleUrl of articleUrls) {
        await this.articlesService.createArticle(
          articleUrl,
          website.id,
          sitemap.id,
        );

        if (scrapping_enabled) {
          await this.queueService.addScrapeJob(articleUrl);
        }
      }
    }

    return website;
  }

  async updateWebsite(
    id: number,
    data: Partial<Website>,
  ): Promise<Website | undefined> {
    const [updatedWebsite] = await this.knex<Website>('websites')
      .where('id', id)
      .update(data)
      .returning('*');
    return updatedWebsite;
  }

  async initiateSitemapsUpdateCheck() {
    const websites = await this.knex<Website>('websites').select(
      'url',
      'id',
      'scrapping_enabled',
      'ad_check_frequency',
    );

    console.log('[Sitemaps Update Check] Start');
    const startTimestamp = performance.now();
    try {
      await Promise.all(
        websites.map((website) => this.checkSitemapsForWebsite(website)),
      );
    } catch (e) {
      console.error('[Sitemaps Update Check] Error:', e);
    } finally {
      const durationMs = performance.now() - startTimestamp;
      console.log(
        `[Sitemaps Update Check] Completed in ${formatMilliseconds(durationMs)}!`,
      );
    }
  }

  @Cron('0 0 * * *') // Every day at midnight
  async sitemapUpdateCheckCron() {
    await this.initiateSitemapsUpdateCheck();
  }

  async getAdCheckResults(websiteUrl: string) {
    const website = await this.knex<Website>('websites')
      .where('url', websiteUrl)
      .first();

    if (!website) {
      throw new HttpException('Website not found', HttpStatus.NOT_FOUND);
    }

    const articles = await this.knex<Article>('articles')
      .where('website_id', website.id)
      .select('url', 'ad_check_result');

    return {
      url: website.url,
      lastAdCheck: website.last_ad_check,
      articles: articles.map((article) => ({
        url: article.url,
        adCheckResult: article.ad_check_result,
      })),
    };
  }

  async requestAdCheck(websiteUrl: string) {
    const website = await this.knex<Website>('websites')
      .where('url', websiteUrl)
      .first();

    if (!website) {
      throw new HttpException('Website not found', HttpStatus.NOT_FOUND);
    }

    const articles = await this.knex<Article>('articles')
      .where('website_id', website.id)
      .select('id', 'url');

    if (articles.length === 0) {
      return {
        success: true,
        message: 'No articles found for this website',
        websiteUrl,
        articlesProcessed: 0,
      };
    }

    // Limit concurrent requests to 100 to avoid Lambda rate limiting
    const limiter = new Bottleneck({
      maxConcurrent: 100,
      minTime: 60, // 60ms between scheduling jobs (similar to staggerMs)
    });

    // Build an array of promises using bottleneck
    const scheduledPromises: Promise<
      AdCheckJobResult | AdCheckJobErrorResult
    >[] = [];

    for (const article of articles) {
      // schedule the job via bottleneck
      const p = limiter.schedule(async () => {
        try {
          const adCheckResult = await this.awsService.invokeAdCheckLambda(
            article.url,
            article.id,
          );

          // maybe batch update instead?
          await this.articlesService.updateArticleById(article.id, {
            ad_check_result: adCheckResult,
          });

          return adCheckResult;
        } catch (error) {
          console.error(`Error processing article ${article.url}:`, error);

          return {
            id: article.id,
            url: article.url,
            timestamp: new Date().toISOString(),
            error: (error as Error).message || 'Unknown error',
          } as AdCheckJobErrorResult;
        }
      });

      scheduledPromises.push(p);
    }

    const results: PromiseSettledResult<AdCheckJobResult>[] =
      await Promise.allSettled(scheduledPromises);

    await this.updateWebsite(website.id, {
      last_ad_check: new Date(),
    });

    const successful = results.filter(
      (r) => r.status === 'fulfilled' && r.value && !('error' in r.value),
    ).length;
    const failed = results.length - successful;

    return {
      message: `Ad check completed for ${articles.length} articles`,
      articlesProcessed: articles.length,
      successful,
      failed,

      // fixme: one more error handling? seems like an unnecessary step
      results: results.map((r) =>
        r.status === 'fulfilled'
          ? r.value
          : { success: false, error: r.reason as string },
      ),
    };
  }

  private async checkSitemapsForWebsite(
    website: Pick<
      Website,
      'id' | 'url' | 'scrapping_enabled' | 'ad_check_frequency'
    >,
  ) {
    await this.updateWebsiteSitemaps(website.id);
    const sitemaps = await this.sitemapsService.getSitemapsByWebsiteId(
      website.id,
    );

    if (sitemaps.length === 0) {
      console.log(
        '[Sitemaps Update Check] No sitemaps found for website: ' + website.url,
      );
      return;
    }

    const articlesMap: Map<number, string[]> = new Map();

    for (const sitemap of sitemaps) {
      const articleUrlsFromSitemap = await articlesFromSitemap(sitemap.url);
      articlesMap.set(sitemap.id, articleUrlsFromSitemap);
    }

    // todo replace by service call
    const existingArticles = await this.knex<Article>('articles')
      .where('website_id', website.id)
      .select('url', 'id');

    const existingArticleUrls = existingArticles.map(({ url }: Article) => url);
    const newArticlesFromAllSitemaps: string[] = [];
    let newArticlesConnectedToSitemapsCount: number = 0;

    for (const [sitemapId, articleUrls] of articlesMap.entries()) {
      for (const articleUrl of articleUrls) {
        if (!existingArticleUrls.includes(articleUrl)) {
          const article = await this.articlesService.createArticle(
            articleUrl,
            website.id,
            sitemapId,
          );

          if (website.scrapping_enabled) {
            await this.queueService.addScrapeJob(articleUrl);
          }

          if (website.ad_check_frequency) {
            await this.queueService.addAdCheckJob(articleUrl, article.id);
          }

          newArticlesFromAllSitemaps.push(articleUrl);
        }

        // Ensure the article is connected to this sitemap
        const article = existingArticles.find(
          (article) => article.url === articleUrl,
        )!;

        if (article) {
          const existingRelation = await this.knex<SitemapArticle>(
            'sitemap_articles',
          )
            .where({ article_id: article.id, sitemap_id: sitemapId })
            .first();

          if (!existingRelation) {
            await this.knex<SitemapArticle>('sitemap_articles').insert({
              article_id: article.id,
              sitemap_id: sitemapId,
            });
            newArticlesConnectedToSitemapsCount++;
          }
        }
      }
    }

    if (newArticlesFromAllSitemaps.length) {
      console.log(
        `[Sitemaps Update Check] Detected ${newArticlesFromAllSitemaps.length} new articles for website: ${website.url}`,
      );
    }

    if (newArticlesConnectedToSitemapsCount) {
      console.log(
        `[Sitemaps Update Check] Detected ${newArticlesConnectedToSitemapsCount} articles connected to sitemaps for website: ${website.url}`,
      );
    }

    // todo: possibly delete articles that doesn't exist in sitemaps anymore
  }

  private async updateWebsiteSitemaps(id: number) {
    const [website] = await this.knex<Website>('websites')
      .where('id', id)
      .select('url');
    const existingSitemaps =
      await this.sitemapsService.getSitemapsByWebsiteId(id);

    const sitemapUrls = await sitemapsFromUrl(website.url);

    const sitemapsToCreate = sitemapUrls.filter(
      (url) => !existingSitemaps.some((sitemap) => sitemap.url === url),
    );
    await Promise.all(
      sitemapsToCreate.map((url) =>
        this.sitemapsService.createSitemap(url, id),
      ),
    );

    if (sitemapsToCreate.length) {
      console.log(
        `[Sitemaps Update Check] Created ${sitemapsToCreate.length} sitemaps for website: ${website.url}`,
      );
    }

    // todo: implement smarter solution for deleting sitemaps
    // for example, marking them as missing and deleting only if they are missing for a week e.g.
    //
    // const sitemapsToDelete = existingSitemaps.filter(
    //   (sitemap) => !sitemapUrls.includes(sitemap.url),
    // );
    // await Promise.all(
    //   sitemapsToDelete.map((sitemap) =>
    //     this.sitemapsService.deleteSitemap(sitemap.id),
    //   ),
    // );
  }
}

